import { LLMRepository } from './LLMRepository';
import { LLMRequest } from './types/llm-request';
import { LLMResponse } from './types/llm-response';
import { GoogleGenAI } from '@google/genai';
import { generateCacheKeyFromScreenshot } from '../common/utils';

export class GeminiLLMRepository implements LLMRepository {
  private readonly apiKey: string;
  private readonly baseURL: string;

  constructor(apiKey: string, baseURL: string) {
    this.apiKey = apiKey;
    this.baseURL = baseURL
  }
  async getLLMResponse(llmRequest: LLMRequest): Promise<LLMResponse> {
    const start = Date.now();

    const cacheKey = generateCacheKeyFromScreenshot(llmRequest, llmRequest.version);

    const ai = new GoogleGenAI({
      apiKey: this.apiKey,
      httpOptions: {
        baseUrl: this.baseURL,
        headers: {
          'cf-aig-cache-key': cacheKey,
          'cf-aig-skip-cache': llmRequest.skipCache.toString(),
        }
      }
    });

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash-lite',
      contents: [
        {
          inlineData: {
            mimeType: 'image/webp',
            data: llmRequest.screenshot,
          },
        },
      ],

      config: {
        topP: 1.0,
        topK: 1,
        temperature: 0.0,
        maxOutputTokens: 32768,
        systemInstruction: llmRequest.prompt,
        responseMimeType: 'application/json',
        responseSchema: {
          "type": "object",
          "description": "Result of the rule‑based filtering task. Contains only the UI elements that remain after all rules are applied.",
          "properties": {
            "screenInfo": {
              "type": ["object", "null"],
              "description": "High‑level metadata about the analysed frame. Place this object first in the output payload.",
              "properties": {
                "title":           { "type": "string" },
                "description":     {
                  "type": ["string"],
                  "description": "Non-interactive, static text providing context, but not a direct instruction or error. Must not be empty."
                },
                "instruction":     {
                  "type": ["string"],
                  "description": "Actionable guidance for the user. Must not be empty."
                },
                "authState": {
                  "type": "string",
                  "description": "Authentication status of the user with respect to the current screen.",
                  "enum": ["authenticated", "not-authenticated"]
                },
                "controlVisibilityRules": {
                  "type": "array",
                  "description": "Audit list of every detected control and its filtering outcome.",
                  "items": {
                    "type": "object",
                    "properties": {
                      "id": {
                        "type": "string",
                        "description": "The control’s unique identifier. Short and human‑readable."
                      },
                      "status": {
                        "type": "string",
                        "enum": ["included", "excluded"],
                        "description": "Whether the control survived the filtering rules."
                      },
                      "reason": {
                        "type": ["string", "null"],
                        "description": "Summarized reason for inclusion or exclusion."
                      }
                    },

                    "required": ["id", "status"]
                  }
                },
                "errors": {
                  "type": ["array", "null"],
                  "items": { "type": "string" },
                  "description": "A list of all visible validation error messages or security notices."
                },
              },
              "required": ["title", "authState", "errors"]
            },

            "controls": {
              "type": "object",
              "description": "Interactive elements.",
              "properties": {
                "fields": {
                  "type": "array",
                  "description": "Input‑type controls kept after filtering.",
                  "items": {
                    "type": "object",
                    "properties": {
                      "id":         { "type": "string" },
                      "order":      { "type": "number", "minimum": 1 },
                      "label":      { "type": "string" },
                      "type":       {
                        "type": "string",
                        "enum": ["select", "text", "password", "number", "checkbox", "checkboxgroup", "radio", "textarea", "other"],
                        "description": "Type of the input control."},
                      "actiontype": { "type": "string", "enum": ["fill", "select"] },
                      "name":       { "type": "string" },
                      "options": {
                        "type": ["array", "null"],
                        "items": {
                          "type": "object",
                          "properties": {
                            "value": { "type": "string" },
                            "label": { "type": "string" }
                          },
                          "required": ["value", "label"]
                        }
                      },
                      "checked":    { "type": "boolean" },
                    },
                    "required": ["id", "order", "label", "type", "actiontype", "name", "checked"],
                    "propertyOrdering": ["id", "order", "label", "type", "actiontype", "name", "checked"]
                  }
                },

                "buttons": {
                  "type": "array",
                  "description": "Button‑type controls kept after filtering.",
                  "items": {
                    "type": "object",
                    "properties": {
                      "id":         { "type": "string" },
                      "order":      { "type": "number", "minimum": 1 },
                      "label":      { "type": "string" },
                      "variant":    { "type": "string", "enum": ["primary", "secondary", "link"] },
                      "type":       { "type": "string", "enum": ["submit", "click", "device-ack"] },
                      "actiontype": { "type": "string", "enum": ["click"] },
                    },
                    "required": ["id", "order", "label", "variant", "type", "actiontype"],
                    "propertyOrdering": ["id", "order", "label", "variant", "type", "actiontype"],

                  }
                }
              },
              "required": ["fields", "buttons"],
              "propertyOrdering": ["fields", "buttons"],

            }
          },

          "required": ["screenInfo", "controls"],
          "propertyOrdering": ["screenInfo", "controls"],
        }

        ,
        seed: 100,
        thinkingConfig: {
          includeThoughts: false,
        }
      },
    });

    const end = Date.now();

    if (!response.text) {
      throw new Error('Invalid response from Gemini API');
    }
    // console.log(response);
    // console.log(response.usageMetadata);
    // console.log(response.promptFeedback);

    return {
      output_text: response.text,
      callDuration: end - start,
    };
  }
}
