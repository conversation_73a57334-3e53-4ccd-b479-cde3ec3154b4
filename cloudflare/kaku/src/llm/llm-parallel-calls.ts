/**
 * Helper functions for making parallel LLM calls with different schemas
 */

import { LLMRepository } from './LLMRepository';
import { LLMRequest } from './types/llm-request';
import { FormVisionResult } from '../form-generation/types/form-interfaces';
import { ClassificationResult } from '../form-generation/types/classification-interfaces';
import {
  EXTRACTION_RESPONSE_SCHEMA,
  CLASSIFICATION_RESPONSE_SCHEMA,
} from './schemas/response-schemas';
import { LLMService } from './LLMService';

/**
 * Makes two parallel LLM calls:
 * 1. Extraction call - extracts form controls and metadata
 * 2. Classification call - provides screen classification and verification codes
 */
export async function makeParallelLLMCalls(
  llmService: LLMService,
  baseLLMRequest: Omit<LLMRequest, 'responseSchema' | 'prompt'>,
  extractionPrompt: string,
  classificationPrompt: string,
): Promise<{
  extractionResult: FormVisionResult;
  classificationResult: ClassificationResult;
}> {
  // Create the two requests with different schemas
  const extractionRequest: LLMRequest = {
    ...baseLLMRequest,
    prompt: extractionPrompt,
    responseSchema: EXTRACTION_RESPONSE_SCHEMA,
  };

  const classificationRequest: LLMRequest = {
    ...baseLLMRequest,
    prompt: classificationPrompt,
    responseSchema: CLASSIFICATION_RESPONSE_SCHEMA,
  };

  // Make both calls in parallel
  const [extractionResponse, classificationResponse] = await Promise.all([
    llmService.getLLMResponse(extractionRequest),
    llmService.getLLMResponse(classificationRequest),
  ]);

  // Parse the responses
  const extractionResult: FormVisionResult = JSON.parse(extractionResponse.output_text);
  const classificationResult: ClassificationResult = JSON.parse(classificationResponse.output_text);

  return {
    extractionResult,
    classificationResult,
  };
}
