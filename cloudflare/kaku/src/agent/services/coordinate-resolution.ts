import { LLMService } from '../../llm/LLMService';
import { PlatformTypes } from '../../ui/constants';
import { ActionWithoutCoordinates, Action } from '../types/extract-result';
import { FormField, FormButton } from '../../form-generation/htmx-generator';

export interface ElementCoordinateRequest {
  fields: FormField[];
  buttons: FormButton[];
}

export interface ElementCoordinateMapping {
  [elementId: string]: {
    x: number;
    y: number;
  };
}

export class CoordinateResolutionService {
  constructor(private llmService: LLMService) {}

  /**
   * New element-based coordinate resolution method
   */
  async resolveElementCoordinates(
    screenshot: string,
    elementRequest: ElementCoordinateRequest,
    platform: PlatformTypes,
    viewportWidth: number,
    viewportHeight: number,
    maxRetries: number = 3,
  ): Promise<ElementCoordinateMapping> {
    const elementsContext = this.buildElementsContext(elementRequest);
    const prompt = this.buildElementCoordinateResolutionPrompt(elementsContext);

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await this.llmService.getLLMResponse({
          platform,
          prompt,
          screenshot,
          skipCache: true,
          viewportWidth,
          viewportHeight,
        });

        const coordinateResults = this.parseElementCoordinateResponse(response.output_text);
        return this.validateElementCoordinates(coordinateResults, elementsContext);
      } catch (error) {
        lastError = error as Error;
        console.error(`Element coordinate resolution attempt ${attempt} failed:`, error);

        if (attempt === maxRetries) {
          throw new Error(
            `Failed to resolve element coordinates after ${maxRetries} attempts for elements: ${elementsContext.map((e) => e.id).join(', ')}. Last error: ${lastError.message}`,
          );
        }

        // Wait before retry (exponential backoff)
        await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }

    throw lastError || new Error('Element coordinate resolution failed');
  }

  private buildElementsContext(elementRequest: ElementCoordinateRequest): Array<{
    id: string;
    type: string;
    label: string;
    actiontype: string;
    fieldType?: string;
    optionIndex?: number;
  }> {
    const elements: Array<{
      id: string;
      type: string;
      label: string;
      actiontype: string;
      fieldType?: string;
    }> = [];

    elementRequest.fields.forEach((field) => {
      if (field.type === 'radio' && field.options && field.options.length > 0) {
        field.options.forEach((option, index) => {
          elements.push({
            id: `${field.id}-${option.value}`,
            type: 'radio-option',
            label: option.label,
            actiontype: field.actiontype,
            fieldType: 'radio',
          });
        });
      } else {
        elements.push({
          id: field.id,
          type: 'field',
          label: field.label,
          actiontype: field.actiontype,
          fieldType: field.type,
        });
      }
    });

    // Add buttons
    elementRequest.buttons.forEach((button) => {
      elements.push({
        id: button.id,
        type: 'button',
        label: button.label,
        actiontype: button.actiontype,
      });
    });

    return elements.sort((a, b) => {
      // Find the order for element a
      const aElement = [...elementRequest.fields, ...elementRequest.buttons].find(
        (el) => el.id === a.id || a.id.startsWith(`${el.id}-`),
      );
      const bElement = [...elementRequest.fields, ...elementRequest.buttons].find(
        (el) => el.id === b.id || b.id.startsWith(`${el.id}-`),
      );

      const aOrder = aElement?.order ?? 999;
      const bOrder = bElement?.order ?? 999;

      return aOrder - bOrder;
    });
  }

  private buildElementCoordinateResolutionPrompt(
    elementsContext: Array<{ id: string; type: string; label: string; actiontype: string }>,
  ): string {
    const elementsJson = JSON.stringify(elementsContext, null, 2);

    return `Find pixel coordinates for UI elements in this screenshot.

Elements to locate:
${elementsJson}

Rules:
- Input fields: center of text box
- Buttons: center of button
- Radio buttons: can also point to the label
- Coordinates must be positive integers

You MUST respond with ONLY this JSON format (no other text):
{
  "password": {"x": 400, "y": 250},
  "login-button": {"x": 400, "y": 320}
}

Include ALL elements. If unsure, estimate typical form positions.

JSON:`;
  }

  private parseElementCoordinateResponse(responseText: string): ElementCoordinateMapping {
    const cleanContent = responseText
      .replace(/```json\s*/g, '')
      .replace(/```\s*$/g, '')
      .trim();

    try {
      const parsed = JSON.parse(cleanContent);
      if (typeof parsed !== 'object' || parsed === null) {
        throw new Error(`Expected JSON object but got: ${typeof parsed}`);
      }
      return parsed as ElementCoordinateMapping;
    } catch (error) {
      throw new Error(
        `Failed to parse element coordinate response as JSON object. Error: ${error}. Clean content: ${cleanContent}. Original response: ${responseText}`,
      );
    }
  }

  private validateElementCoordinates(
    coordinateResults: ElementCoordinateMapping,
    elementsContext: Array<{ id: string; type: string; label: string; actiontype: string }>,
  ): ElementCoordinateMapping {
    // Validate that all expected elements have coordinates
    for (const element of elementsContext) {
      const elementCoord = coordinateResults[element.id];

      if (!elementCoord) {
        throw new Error(`Missing coordinates for element: ${element.id}`);
      }
    }

    return coordinateResults;
  }

  async resolveCoordinates(
    screenshot: string,
    actions: ActionWithoutCoordinates[],
    platform: PlatformTypes,
    viewportWidth: number,
    viewportHeight: number,
    maxRetries: number = 3,
  ): Promise<Action[]> {
    const actionsContext = actions.map((action) => ({
      type: action.type,
      name: action.name,
      order: action.order,
      isSubmitAction: action.isSubmitAction,
    }));

    const prompt = this.buildCoordinateResolutionPrompt(actionsContext);

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await this.llmService.getLLMResponse({
          platform,
          prompt,
          screenshot,
          skipCache: true,
          viewportWidth,
          viewportHeight,
          
        });

        console.log({
          coordinateResolutionResponse: JSON.stringify(response.output_text),
          outputText: response.output_text,
          attempt,
          actionsToResolve: actionsContext,
        });

        const coordinateResults = this.parseCoordinateResponse(response.output_text);
        return this.mapCoordinatesToActions(actions, coordinateResults);
      } catch (error) {
        lastError = error as Error;
        console.error(`Coordinate resolution attempt ${attempt} failed:`, error);

        if (attempt === maxRetries) {
          throw new Error(
            `Failed to resolve coordinates after ${maxRetries} attempts for actions: ${actionsContext.map((a) => a.name).join(', ')}. Last error: ${lastError.message}`,
          );
        }

        // Wait before retry (exponential backoff)
        await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }

    throw lastError || new Error('Coordinate resolution failed');
  }

  private buildCoordinateResolutionPrompt(actionsContext: any[]): string {
    return `You are a precise coordinate calculator for browser automation. Your task is to analyze the screenshot and calculate exact pixel coordinates for each action.

## STEP-BY-STEP PROCESS:

### STEP 1: ANALYZE EACH ACTION NAME
For each action below, understand what UI element it refers to:
- "login", "email", "username" → input field for username/email
- "password" → password input field
- "Sign in", "Login", "Submit" → submit/login button
- "Continue", "Next" → action button
- "code", "otp", "verification" → OTP/code input field

### STEP 2: ANALYZE THE SCREENSHOT
Carefully examine the screenshot to identify:
- Input fields (text boxes, password fields)
- Buttons (clickable elements with text)
- Form elements and their visual boundaries
- Text labels that help identify field purposes

### STEP 3: MAP ACTIONS TO VISUAL ELEMENTS
For each action, find the corresponding visual element in the screenshot:
- Match action names to visible form elements
- Look for placeholder text, labels, or context clues
- Identify the exact clickable/fillable area

### STEP 4: CALCULATE PRECISE COORDINATES
For each mapped element, calculate the CENTER coordinates:
- Input fields: Center of the input box area
- Buttons: Center of the button clickable area
- Ensure coordinates are within visible viewport bounds
- Use exact pixel positions (integers only)

### STEP 5: RETURN RESULTS
Return a JSON array with the same actions plus coordinates.

## ACTIONS TO PROCESS:
${JSON.stringify(actionsContext, null, 2)}

## REQUIRED OUTPUT FORMAT:
Return ONLY a JSON array (no markdown, no explanations):
[
  {
    "type": "fill",
    "name": "login",
    "order": 0,
    "isSubmitAction": false,
    "coordinates": {"x": 123, "y": 456}
  },
  {
    "type": "click",
    "name": "Sign in",
    "order": 1,
    "isSubmitAction": true,
    "coordinates": {"x": 789, "y": 012}
  }
]

CRITICAL: You must find and return coordinates for ALL actions. If you cannot locate an element, estimate based on typical form layouts, but you MUST provide coordinates for every action.`;
  }

  private parseCoordinateResponse(responseText: string): Action[] {
    const cleanContent = responseText
      .replace(/```json\s*/g, '')
      .replace(/```\s*$/g, '')
      .trim();

    try {
      const parsed = JSON.parse(cleanContent);
      if (!Array.isArray(parsed)) {
        throw new Error(`Expected JSON array but got: ${typeof parsed}`);
      }
      return parsed;
    } catch (error) {
      throw new Error(
        `Failed to parse coordinate response as JSON array. Error: ${error}. Clean content: ${cleanContent}. Original response: ${responseText}`,
      );
    }
  }

  private mapCoordinatesToActions(
    originalActions: ActionWithoutCoordinates[],
    coordinateResults: Action[],
  ): Action[] {
    if (coordinateResults.length !== originalActions.length) {
      throw new Error(
        `Coordinate results length (${coordinateResults.length}) does not match original actions length (${originalActions.length})`,
      );
    }

    return originalActions.map((action, index) => {
      const coordinateResult = coordinateResults[index];

      if (!coordinateResult?.coordinates) {
        throw new Error(`Missing coordinates for action at index ${index}: ${action.name}`);
      }

      if (
        typeof coordinateResult.coordinates.x !== 'number' ||
        typeof coordinateResult.coordinates.y !== 'number'
      ) {
        throw new Error(
          `Invalid coordinates for action ${action.name}: ${JSON.stringify(coordinateResult.coordinates)}`,
        );
      }

      // Validate coordinates are reasonable (not negative, not too large)
      if (coordinateResult.coordinates.x < 0 || coordinateResult.coordinates.y < 0) {
        throw new Error(
          `Negative coordinates for action ${action.name}: ${JSON.stringify(coordinateResult.coordinates)}`,
        );
      }

      if (coordinateResult.coordinates.x > 5000 || coordinateResult.coordinates.y > 5000) {
        throw new Error(
          `Unreasonably large coordinates for action ${action.name}: ${JSON.stringify(coordinateResult.coordinates)}`,
        );
      }

      // Validate that the action names match (optional but helpful for debugging)
      if (coordinateResult.name && coordinateResult.name !== action.name) {
        console.warn(
          `Action name mismatch: expected "${action.name}", got "${coordinateResult.name}"`,
        );
      }

      return {
        ...action,
        coordinates: coordinateResult.coordinates,
      } as Action;
    });
  }
}
